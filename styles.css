/* CSS Variables for Theme Support */
:root {
    --primary-color: #4f46e5;
    --secondary-color: #7c3aed;
    --accent-color: #f59e0b;
    --background-color: #0f0f23;
    --surface-color: #1a1a2e;
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --border-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

[data-theme="light"] {
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--background-color);
    color: var(--text-primary);
    overflow: hidden;
    transition: all 0.3s ease;
}

.hidden {
    display: none !important;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.solar-system-loader {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 2rem;
}

.solar-system-loader .sun {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #ffd700, #ff8c00);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 20px #ffd700;
    animation: pulse 2s ease-in-out infinite;
}

.solar-system-loader .planet {
    position: absolute;
    border-radius: 50%;
    animation: orbit 3s linear infinite;
}

.planet-1 {
    top: 30px;
    left: 30px;
    width: 8px;
    height: 8px;
    background: #8c7853;
    animation-duration: 2s;
}

.planet-2 {
    top: 20px;
    left: 20px;
    width: 12px;
    height: 12px;
    background: #ffc649;
    animation-duration: 3s;
}

.planet-3 {
    top: 10px;
    left: 10px;
    width: 14px;
    height: 14px;
    background: #6b93d6;
    animation-duration: 4s;
}

@keyframes orbit {
    from { transform: rotate(0deg) translateX(40px) rotate(0deg); }
    to { transform: rotate(360deg) translateX(40px) rotate(-360deg); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 1rem auto;
}

.loading-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Main App Layout */
#app {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(15, 15, 35, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.title {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    background: var(--surface-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    border: 1px solid var(--border-color);
    min-height: 44px;
    user-select: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-color);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    padding: 0.75rem;
    min-width: 44px;
    min-height: 44px;
    justify-content: center;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.btn-secondary {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
}

/* Canvas Container */
#canvas-container {
    position: relative;
    flex: 1;
    margin-top: 80px;
    overflow: hidden;
}

#solar-system-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* Tooltip */
.tooltip {
    position: absolute;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    color: var(--text-primary);
    pointer-events: none;
    z-index: 100;
    max-width: 300px;
    box-shadow: 0 8px 32px var(--shadow-color);
}

.tooltip h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--accent-color);
}

.tooltip-stats {
    margin-top: 0.75rem;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    color: var(--text-primary);
    font-weight: 500;
}

/* Camera Info */
.camera-info {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.control-hint {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.control-hint:last-child {
    margin-bottom: 0;
}

/* Control Toggle Button (works on all devices) */
.control-toggle-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
    display: none; /* Hidden by default, shown when panel is closed */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    transition: all 0.3s ease;
    cursor: pointer;
}

.control-toggle-btn.show {
    display: flex;
    animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.control-toggle-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.control-toggle-btn:active {
    transform: scale(0.95);
}

.control-toggle-btn i {
    font-size: 1.2rem;
}

.control-toggle-btn span {
    font-size: 0.7rem;
    line-height: 1;
}

/* Help Tooltip */
.help-tooltip {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(26, 26, 46, 0.98);
    backdrop-filter: blur(15px);
    border: 2px solid var(--accent-color);
    border-radius: 12px;
    padding: 2rem;
    max-width: 400px;
    z-index: 2000;
    display: none;
}

.help-tooltip.show {
    display: block;
    animation: fadeInScale 0.3s ease-out;
}

.tooltip-content {
    text-align: center;
}

.tooltip-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.tooltip-content strong {
    color: var(--accent-color);
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.mobile-control-btn i {
    font-size: 1.2rem;
}

.mobile-control-btn span {
    font-size: 0.7rem;
    line-height: 1;
}

/* Control Panel */
.control-panel {
    position: fixed;
    top: 80px;
    right: 0;
    width: 380px;
    height: calc(100vh - 80px);
    background: rgba(26, 26, 46, 0.98);
    backdrop-filter: blur(15px);
    border-left: 1px solid var(--border-color);
    transform: translateX(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 900;
    overflow-y: auto;
    overflow-x: hidden;
}

.control-panel.collapsed {
    transform: translateX(340px);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    background: rgba(26, 26, 46, 0.98);
    backdrop-filter: blur(15px);
    z-index: 10;
}

.panel-header h2 {
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--accent-color);
    margin: 0;
}

.panel-content {
    padding: 1rem 1.5rem 2rem;
}

.control-section {
    margin-bottom: 1.5rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.02);
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.control-section h3 {
    font-size: 0.95rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-section h3 i {
    color: var(--accent-color);
    font-size: 0.9rem;
}

/* Collapsible sections */
.collapsible .section-toggle {
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    justify-content: space-between;
}

.collapsible .section-toggle:hover {
    color: var(--accent-color);
}

.collapsible .toggle-icon {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.collapsible.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.collapsible.collapsed .section-content {
    display: none;
}

.control-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.control-item {
    margin-bottom: 1.25rem;
}

.control-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.control-item label i {
    color: var(--accent-color);
    font-size: 0.8rem;
    width: 14px;
}

.status-text {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: block;
}

/* Sliders */
.slider-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

input[type="range"] {
    flex: 1;
    height: 8px;
    border-radius: 4px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background: var(--gradient-primary);
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
    transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

input[type="range"]::-moz-range-thumb {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background: var(--gradient-primary);
    cursor: pointer;
    border: none;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
    transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.1);
}

.slider-value {
    min-width: 40px;
    text-align: right;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--accent-color);
}

/* Checkboxes */
.checkbox-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.checkbox-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--accent-color);
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: scale(1.1);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    line-height: 1.2;
}

.checkbox-label i {
    color: var(--accent-color);
    font-size: 0.75rem;
    width: 12px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--surface-color);
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-family: 'Orbitron', monospace;
    color: var(--accent-color);
}

.modal-body {
    padding: 1.5rem;
    line-height: 1.6;
}

.modal-body h3 {
    margin: 1.5rem 0 0.75rem 0;
    color: var(--text-primary);
}

.modal-body ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.modal-body li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Mobile Design */
@media (max-width: 768px) {
    .control-panel {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 1000;
        transform: translateX(0);
        border: none;
        background: rgba(26, 26, 46, 0.98);
    }

    .control-panel.collapsed {
        transform: translateX(100%);
    }

    .mobile-control-btn {
        display: flex;
    }

    .panel-header {
        padding-top: calc(env(safe-area-inset-top) + 1rem);
        background: rgba(26, 26, 46, 1);
        border-bottom: 2px solid var(--accent-color);
    }

    .panel-content {
        padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
    }
}

/* Additional mobile optimizations */
@media (max-width: 768px) {
    .header {
        padding: 0.75rem 1rem;
    }

    .title {
        font-size: 1.25rem;
    }

    .control-section {
        margin-bottom: 1rem;
        padding: 0.75rem;
    }

    .control-group {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .checkbox-item {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .camera-info {
        display: none;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .control-toggle-btn {
        bottom: calc(env(safe-area-inset-bottom) + 20px);
        right: 20px;
    }
}

@media (max-width: 480px) {
    .header-controls {
        gap: 0.25rem;
    }
    
    .btn-icon {
        width: 36px;
        height: 36px;
        padding: 0.375rem;
    }
    
    .panel-content {
        padding: 1rem;
    }
}
