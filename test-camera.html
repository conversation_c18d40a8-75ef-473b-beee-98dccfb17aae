<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Speed Test</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #controls { position: fixed; top: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 10px; }
        #canvas-container { width: 100%; height: 500px; border: 1px solid #ccc; margin-top: 20px; }
        .control-item { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="range"] { width: 200px; }
        .value { color: #4CAF50; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Camera Speed Test</h1>
    <p>This is a simple test to verify that camera speed controls are working properly.</p>
    
    <div id="controls">
        <div class="control-item">
            <label for="camera-speed">Camera Speed:</label>
            <input type="range" id="camera-speed" min="0.1" max="3" step="0.1" value="1">
            <span class="value" id="speed-value">1.0x</span>
        </div>
        <div class="control-item">
            <button id="test-btn">Test Camera Movement</button>
        </div>
        <div class="control-item">
            <div id="status">Status: Ready</div>
        </div>
    </div>

    <div id="canvas-container"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script>
        let scene, camera, renderer, controls, cube;
        let currentCameraSpeed = 1.0;

        function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(5, 5, 5);

            // Create renderer
            const container = document.getElementById('canvas-container');
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            container.appendChild(renderer.domElement);

            // Create a test cube
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            cube = new THREE.Mesh(geometry, material);
            scene.add(cube);

            // Add lighting
            const light = new THREE.DirectionalLight(0xffffff, 1);
            light.position.set(5, 5, 5);
            scene.add(light);

            // Setup controls
            try {
                controls = new THREE.OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.rotateSpeed = currentCameraSpeed;
                controls.zoomSpeed = currentCameraSpeed;
                controls.panSpeed = currentCameraSpeed;
                
                document.getElementById('status').textContent = 'Status: OrbitControls loaded successfully';
                console.log('OrbitControls properties:', {
                    rotateSpeed: controls.rotateSpeed,
                    zoomSpeed: controls.zoomSpeed,
                    panSpeed: controls.panSpeed
                });
            } catch (error) {
                document.getElementById('status').textContent = 'Status: Error loading OrbitControls - ' + error.message;
                console.error('OrbitControls error:', error);
            }

            // Setup event listeners
            const speedSlider = document.getElementById('camera-speed');
            const speedValue = document.getElementById('speed-value');
            const testBtn = document.getElementById('test-btn');

            speedSlider.addEventListener('input', (e) => {
                const speed = parseFloat(e.target.value);
                speedValue.textContent = speed.toFixed(1) + 'x';
                setCameraSpeed(speed);
            });

            testBtn.addEventListener('click', testCameraMovement);

            // Start animation
            animate();
        }

        function setCameraSpeed(speed) {
            currentCameraSpeed = speed;
            if (controls) {
                controls.rotateSpeed = speed;
                controls.zoomSpeed = speed;
                controls.panSpeed = speed;
                
                document.getElementById('status').textContent = `Status: Camera speed set to ${speed.toFixed(1)}x`;
                console.log('Camera speed updated:', {
                    rotateSpeed: controls.rotateSpeed,
                    zoomSpeed: controls.zoomSpeed,
                    panSpeed: controls.panSpeed
                });
            }
        }

        function testCameraMovement() {
            if (controls) {
                // Animate camera position to test if speed affects movement
                const startPos = camera.position.clone();
                const targetPos = new THREE.Vector3(
                    startPos.x + (Math.random() - 0.5) * 10,
                    startPos.y + (Math.random() - 0.5) * 10,
                    startPos.z + (Math.random() - 0.5) * 10
                );
                
                // Simple animation to test camera movement
                let progress = 0;
                const animateCamera = () => {
                    progress += 0.02 * currentCameraSpeed;
                    if (progress < 1) {
                        camera.position.lerpVectors(startPos, targetPos, progress);
                        requestAnimationFrame(animateCamera);
                    }
                };
                animateCamera();
                
                document.getElementById('status').textContent = `Status: Testing camera movement at ${currentCameraSpeed.toFixed(1)}x speed`;
            }
        }

        function animate() {
            requestAnimationFrame(animate);
            
            // Rotate cube
            cube.rotation.x += 0.01;
            cube.rotation.y += 0.01;
            
            if (controls) {
                controls.update();
            }
            
            renderer.render(scene, camera);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            const container = document.getElementById('canvas-container');
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        });

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
