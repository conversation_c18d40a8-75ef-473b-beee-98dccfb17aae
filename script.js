// 3D Solar System Simulation
class SolarSystemSimulation {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.clock = new THREE.Clock();
        
        // Animation state
        this.isPlaying = true;
        this.globalSpeed = 1.0;
        this.cameraSpeed = 1.0;
        
        // Solar system objects
        this.sun = null;
        this.planets = [];
        this.planetData = [];
        this.starField = null;
        this.orbitLines = [];
        this.planetTrails = [];
        
        // UI state
        this.showOrbits = true;
        this.showLabels = true;
        this.showStars = true;
        this.showTrails = true;
        this.realisticSizes = false;
        this.isDarkTheme = true;
        
        // Mouse interaction
        this.mouse = new THREE.Vector2();
        this.raycaster = new THREE.Raycaster();
        this.hoveredPlanet = null;

        // Performance monitoring
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        this.currentFPS = 60;
        
        this.init();
    }

    async init() {
        this.showLoadingScreen();
        this.updateLoadingProgress(10, 'Initializing 3D Scene...');

        await this.setupScene();
        this.updateLoadingProgress(30, 'Creating Solar System...');

        await this.createSolarSystem();
        this.updateLoadingProgress(70, 'Setting up Controls...');

        this.setupControls();
        this.updateLoadingProgress(85, 'Preparing Interface...');

        this.setupEventListeners();
        this.updateLoadingProgress(95, 'Starting Simulation...');

        this.animate();
        this.updateLoadingProgress(100, 'Ready!');

        setTimeout(() => this.hideLoadingScreen(), 500);
    }

    showLoadingScreen() {
        document.getElementById('loading-screen').style.display = 'flex';
        document.getElementById('app').classList.add('hidden');
    }

    updateLoadingProgress(percentage, message) {
        const progressBar = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-content h2');

        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    hideLoadingScreen() {
        setTimeout(() => {
            document.getElementById('loading-screen').style.display = 'none';
            document.getElementById('app').classList.remove('hidden');
        }, 1000);
    }

    setupScene() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            10000
        );
        this.camera.position.set(0, 50, 100);

        // Create renderer
        const canvas = document.getElementById('solar-system-canvas');
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight - 80);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 0.5;

        // Performance optimizations
        this.renderer.powerPreference = "high-performance";
        this.renderer.antialias = window.devicePixelRatio === 1;

        // Setup lighting
        this.setupLighting();

        // Create starfield
        this.createStarField();
    }

    setupLighting() {
        // Ambient light for general illumination (space is mostly dark)
        const ambientLight = new THREE.AmbientLight(0x202040, 0.03);
        this.scene.add(ambientLight);

        // Primary sun light with dynamic intensity
        const sunLight = new THREE.PointLight(0xfff4e6, 4, 2500);
        sunLight.position.set(0, 0, 0);
        sunLight.castShadow = true;
        sunLight.shadow.mapSize.width = 4096;
        sunLight.shadow.mapSize.height = 4096;
        sunLight.shadow.camera.near = 0.1;
        sunLight.shadow.camera.far = 2500;
        sunLight.shadow.bias = -0.0001;
        this.scene.add(sunLight);
        this.sunLight = sunLight;

        // Secondary sun light for enhanced illumination
        const sunLight2 = new THREE.PointLight(0xffaa44, 2, 1500);
        sunLight2.position.set(0, 0, 0);
        this.scene.add(sunLight2);
        this.sunLight2 = sunLight2;

        // Solar flare light (will be animated)
        const flareLight = new THREE.PointLight(0xff6600, 0, 800);
        flareLight.position.set(0, 0, 0);
        this.scene.add(flareLight);
        this.flareLight = flareLight;

        // Subtle fill light to prevent completely black shadows
        const fillLight = new THREE.DirectionalLight(0x4169e1, 0.08);
        fillLight.position.set(-50, 30, 50);
        this.scene.add(fillLight);

        // Corona light for atmospheric effects
        const coronaLight = new THREE.PointLight(0xffd700, 1, 3000);
        coronaLight.position.set(0, 0, 0);
        this.scene.add(coronaLight);
        this.coronaLight = coronaLight;

        // Add realistic starlight
        const starLight = new THREE.AmbientLight(0x1a1a2e, 0.01);
        this.scene.add(starLight);

        // Store base intensities for dynamic effects
        this.baseSunIntensity = 4;
        this.baseFlareIntensity = 0;
        this.baseCoronaIntensity = 1;
    }

    createStarField() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 10000;
        const positions = new Float32Array(starCount * 3);
        const colors = new Float32Array(starCount * 3);

        for (let i = 0; i < starCount * 3; i += 3) {
            // Random position in sphere
            const radius = 2000 + Math.random() * 3000;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.acos(2 * Math.random() - 1);

            positions[i] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i + 2] = radius * Math.cos(phi);

            // Random star color (white to blue-white)
            const intensity = 0.5 + Math.random() * 0.5;
            colors[i] = intensity;
            colors[i + 1] = intensity;
            colors[i + 2] = intensity + Math.random() * 0.3;
        }

        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        starGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const starMaterial = new THREE.PointsMaterial({
            size: 2,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });

        this.starField = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(this.starField);
    }

    createPlanetTrails() {
        this.planetTrails = [];

        this.planetData.forEach((data, index) => {
            const trailGeometry = new THREE.BufferGeometry();
            const trailMaterial = new THREE.LineBasicMaterial({
                color: data.color,
                transparent: true,
                opacity: 0.3,
                linewidth: 2
            });

            const maxTrailLength = 100;
            const positions = new Float32Array(maxTrailLength * 3);
            trailGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

            const trail = new THREE.Line(trailGeometry, trailMaterial);
            trail.userData = {
                planetIndex: index,
                positions: [],
                maxLength: maxTrailLength
            };

            this.planetTrails.push(trail);
            this.scene.add(trail);
        });
    }

    async createSolarSystem() {
        // Planet data with realistic properties
        this.planetData = [
            {
                name: 'Mercury',
                radius: 0.38,
                distance: 15,
                speed: 4.74,
                rotationSpeed: 0.01,
                color: 0x8c7853,
                info: 'The smallest planet and closest to the Sun.',
                realDistance: '57.9 million km',
                realPeriod: '88 days'
            },
            {
                name: 'Venus',
                radius: 0.95,
                distance: 22,
                speed: 3.50,
                rotationSpeed: -0.004,
                color: 0xffc649,
                info: 'The hottest planet with a thick, toxic atmosphere.',
                realDistance: '108.2 million km',
                realPeriod: '225 days'
            },
            {
                name: 'Earth',
                radius: 1.0,
                distance: 30,
                speed: 2.98,
                rotationSpeed: 0.02,
                color: 0x6b93d6,
                info: 'Our home planet, the only known planet with life.',
                realDistance: '149.6 million km',
                realPeriod: '365 days'
            },
            {
                name: 'Mars',
                radius: 0.53,
                distance: 45,
                speed: 2.41,
                rotationSpeed: 0.018,
                color: 0xcd5c5c,
                info: 'The Red Planet, with the largest volcano in the solar system.',
                realDistance: '227.9 million km',
                realPeriod: '687 days'
            },
            {
                name: 'Jupiter',
                radius: 2.5,
                distance: 70,
                speed: 1.31,
                rotationSpeed: 0.04,
                color: 0xd8ca9d,
                info: 'The largest planet, a gas giant with over 80 moons.',
                realDistance: '778.5 million km',
                realPeriod: '12 years'
            },
            {
                name: 'Saturn',
                radius: 2.1,
                distance: 95,
                speed: 0.97,
                rotationSpeed: 0.038,
                color: 0xfad5a5,
                info: 'Famous for its prominent ring system.',
                realDistance: '1.43 billion km',
                realPeriod: '29 years'
            },
            {
                name: 'Uranus',
                radius: 1.6,
                distance: 120,
                speed: 0.68,
                rotationSpeed: 0.03,
                color: 0x4fd0e7,
                info: 'An ice giant that rotates on its side.',
                realDistance: '2.87 billion km',
                realPeriod: '84 years'
            },
            {
                name: 'Neptune',
                radius: 1.5,
                distance: 150,
                speed: 0.54,
                rotationSpeed: 0.032,
                color: 0x4b70dd,
                info: 'The windiest planet with speeds up to 2,100 km/h.',
                realDistance: '4.50 billion km',
                realPeriod: '165 years'
            }
        ];

        // Create the Sun
        await this.createSun();

        // Create planets
        for (let i = 0; i < this.planetData.length; i++) {
            await this.createPlanet(this.planetData[i], i);
        }

        // Create orbit lines
        this.createOrbitLines();

        // Create planet trails
        this.createPlanetTrails();
    }

    async createSun() {
        const sunGeometry = new THREE.SphereGeometry(5, 128, 128);

        // Create spectacular sun material with enhanced lighting effects
        const sunMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                color1: { value: new THREE.Color(0xff2200) }, // Deep red-orange
                color2: { value: new THREE.Color(0xff6600) }, // Bright orange
                color3: { value: new THREE.Color(0xffaa00) }, // Golden yellow
                color4: { value: new THREE.Color(0xffff88) }, // Bright yellow
                color5: { value: new THREE.Color(0xffffff) }, // White hot
                noiseScale: { value: 3.0 },
                intensity: { value: 2.5 },
                pulseIntensity: { value: 0.3 },
                flareIntensity: { value: 1.0 }
            },
            vertexShader: `
                varying vec2 vUv;
                varying vec3 vPosition;
                varying vec3 vNormal;

                void main() {
                    vUv = uv;
                    vPosition = position;
                    vNormal = normal;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform vec3 color1;
                uniform vec3 color2;
                uniform vec3 color3;
                uniform vec3 color4;
                uniform vec3 color5;
                uniform float noiseScale;
                uniform float intensity;
                uniform float pulseIntensity;
                uniform float flareIntensity;

                varying vec2 vUv;
                varying vec3 vPosition;
                varying vec3 vNormal;

                // Enhanced noise functions for more dynamic effects
                float noise(vec3 p) {
                    return sin(p.x * 12.0 + time * 2.0) * sin(p.y * 8.0 + time * 1.5) * sin(p.z * 10.0 + time * 1.8);
                }

                float turbulence(vec3 p) {
                    float t = 0.0;
                    float amplitude = 1.0;
                    for (int i = 0; i < 4; i++) {
                        t += amplitude * abs(noise(p));
                        p *= 2.0;
                        amplitude *= 0.5;
                    }
                    return t;
                }

                float fbm(vec3 p) {
                    float value = 0.0;
                    float amplitude = 0.5;
                    for (int i = 0; i < 6; i++) {
                        value += amplitude * noise(p);
                        p *= 2.0;
                        amplitude *= 0.5;
                    }
                    return value;
                }

                void main() {
                    vec3 pos = vPosition * noiseScale;

                    // Multiple noise layers for complex surface
                    float n1 = fbm(pos + time * 0.1);
                    float n2 = turbulence(pos * 1.5 + time * 0.08);
                    float n3 = noise(pos * 3.0 + time * 0.12);
                    float n4 = noise(pos * 6.0 + time * 0.2);

                    // Combine noise for solar activity
                    float solarActivity = (n1 + n2 * 0.7 + n3 * 0.4 + n4 * 0.2) / 2.3;

                    // Create temperature zones
                    vec3 finalColor;
                    if (solarActivity > 0.6) {
                        // Hottest regions - white/bright yellow
                        finalColor = mix(color4, color5, (solarActivity - 0.6) / 0.4);
                    } else if (solarActivity > 0.2) {
                        // Hot regions - yellow/orange
                        finalColor = mix(color3, color4, (solarActivity - 0.2) / 0.4);
                    } else if (solarActivity > -0.2) {
                        // Medium regions - orange
                        finalColor = mix(color2, color3, (solarActivity + 0.2) / 0.4);
                    } else {
                        // Cooler regions - red/orange
                        finalColor = mix(color1, color2, (solarActivity + 1.0) / 0.8);
                    }

                    // Add dramatic rim lighting
                    float rim = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
                    rim = pow(rim, 1.5);
                    finalColor += rim * color4 * 0.8;

                    // Add pulsing effect
                    float pulse = sin(time * 3.0) * pulseIntensity + 1.0;

                    // Add solar flare hotspots
                    float flareNoise = noise(pos * 8.0 + time * 0.5);
                    if (flareNoise > 0.7) {
                        finalColor += color5 * (flareNoise - 0.7) * flareIntensity * 2.0;
                    }

                    // Enhanced intensity with dynamic effects
                    float finalIntensity = intensity * pulse;

                    gl_FragColor = vec4(finalColor * finalIntensity, 1.0);
                }
            `,
            side: THREE.FrontSide
        });

        this.sun = new THREE.Mesh(sunGeometry, sunMaterial);
        this.sun.name = 'Sun';
        this.sun.userData.material = sunMaterial; // Store reference for animation
        this.scene.add(this.sun);

        // Create multiple corona layers for realistic effect
        this.createSunCorona();

        // Add solar flares
        this.createSolarFlares();

        // Add lens flare effects
        this.createLensFlare();

        // Add spectacular sun glow effect
        this.createSunGlow();
    }

    createSunCorona() {
        // Inner corona with enhanced effects
        const innerCoronaGeometry = new THREE.SphereGeometry(7, 64, 64);
        const innerCoronaMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                opacity: { value: 0.25 },
                intensity: { value: 1.5 }
            },
            vertexShader: `
                varying vec3 vNormal;
                varying vec2 vUv;
                varying vec3 vPosition;
                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    vUv = uv;
                    vPosition = position;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float opacity;
                uniform float intensity;
                varying vec3 vNormal;
                varying vec2 vUv;
                varying vec3 vPosition;

                float noise(vec3 p) {
                    return sin(p.x * 8.0 + time * 2.0) * sin(p.y * 6.0 + time * 1.5) * sin(p.z * 4.0 + time * 1.8);
                }

                void main() {
                    float rim = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
                    rim = pow(rim, 1.5);

                    // Add dynamic corona activity
                    float activity = noise(vPosition * 2.0 + time * 0.5);
                    float coronaIntensity = rim * (0.8 + activity * 0.4) * intensity;

                    // Dynamic color based on activity
                    vec3 color1 = vec3(1.0, 0.9, 0.4); // Golden
                    vec3 color2 = vec3(1.0, 0.6, 0.2); // Orange
                    vec3 color3 = vec3(1.0, 0.3, 0.1); // Red-orange

                    vec3 color;
                    if (activity > 0.3) {
                        color = mix(color2, color1, (activity - 0.3) / 0.7);
                    } else {
                        color = mix(color3, color2, (activity + 1.0) / 1.3);
                    }

                    // Add pulsing effect
                    float pulse = sin(time * 4.0) * 0.2 + 1.0;

                    gl_FragColor = vec4(color * coronaIntensity, opacity * coronaIntensity * pulse);
                }
            `,
            transparent: true,
            side: THREE.BackSide,
            blending: THREE.AdditiveBlending
        });

        const innerCorona = new THREE.Mesh(innerCoronaGeometry, innerCoronaMaterial);
        innerCorona.name = 'InnerCorona';
        this.sun.add(innerCorona);

        // Outer corona with enhanced streaming effects
        const outerCoronaGeometry = new THREE.SphereGeometry(11, 64, 64);
        const outerCoronaMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                opacity: { value: 0.15 },
                streamIntensity: { value: 1.2 }
            },
            vertexShader: `
                varying vec3 vNormal;
                varying vec2 vUv;
                varying vec3 vPosition;
                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    vUv = uv;
                    vPosition = position;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float opacity;
                uniform float streamIntensity;
                varying vec3 vNormal;
                varying vec2 vUv;
                varying vec3 vPosition;

                float noise(vec3 p) {
                    return sin(p.x * 4.0 + time) * sin(p.y * 3.0 + time * 0.8) * sin(p.z * 2.0 + time * 1.2);
                }

                void main() {
                    float rim = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
                    rim = pow(rim, 2.5);

                    // Create streaming corona effects
                    float stream1 = noise(vPosition * 1.0 + time * 0.3);
                    float stream2 = noise(vPosition * 0.5 + time * 0.2);
                    float streaming = (stream1 + stream2) * 0.5;

                    float coronaIntensity = rim * (0.6 + streaming * 0.6) * streamIntensity;

                    // Outer corona colors - more diffuse
                    vec3 color = vec3(1.0, 0.95, 0.7) * coronaIntensity;

                    // Add subtle pulsing
                    float pulse = sin(time * 2.5) * 0.15 + 1.0;

                    gl_FragColor = vec4(color, opacity * coronaIntensity * pulse);
                }
            `,
            transparent: true,
            side: THREE.BackSide,
            blending: THREE.AdditiveBlending
        });

        const outerCorona = new THREE.Mesh(outerCoronaGeometry, outerCoronaMaterial);
        outerCorona.name = 'OuterCorona';
        this.sun.add(outerCorona);

        // Add extreme outer corona for dramatic effect
        const extremeCoronaGeometry = new THREE.SphereGeometry(15, 32, 32);
        const extremeCoronaMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                opacity: { value: 0.05 }
            },
            vertexShader: `
                varying vec3 vNormal;
                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float opacity;
                varying vec3 vNormal;

                void main() {
                    float rim = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
                    rim = pow(rim, 4.0);

                    vec3 color = vec3(1.0, 0.98, 0.8);
                    float pulse = sin(time * 1.5) * 0.3 + 1.0;

                    gl_FragColor = vec4(color * rim, opacity * rim * pulse);
                }
            `,
            transparent: true,
            side: THREE.BackSide,
            blending: THREE.AdditiveBlending
        });

        const extremeCorona = new THREE.Mesh(extremeCoronaGeometry, extremeCoronaMaterial);
        extremeCorona.name = 'ExtremeCorona';
        this.sun.add(extremeCorona);
    }

    createSolarFlares() {
        // Create multiple layers of solar flare effects
        this.createSolarFlareParticles();
        this.createCoronalMassEjections();
        this.createSolarProminences();
    }

    createSolarFlareParticles() {
        // Enhanced particle system for solar flares
        const flareCount = 500;
        const flareGeometry = new THREE.BufferGeometry();
        const positions = new Float32Array(flareCount * 3);
        const colors = new Float32Array(flareCount * 3);
        const sizes = new Float32Array(flareCount);
        const velocities = new Float32Array(flareCount * 3);

        for (let i = 0; i < flareCount; i++) {
            // Random positions around the sun surface
            const radius = 5.2 + Math.random() * 1.5;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.random() * Math.PI;

            positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i * 3 + 2] = radius * Math.cos(phi);

            // Enhanced color palette for more dramatic effect
            const intensity = Math.random();
            if (intensity > 0.8) {
                // White hot flares
                colors[i * 3] = 1.0;
                colors[i * 3 + 1] = 1.0;
                colors[i * 3 + 2] = 0.9;
            } else if (intensity > 0.5) {
                // Bright yellow flares
                colors[i * 3] = 1.0;
                colors[i * 3 + 1] = 0.9;
                colors[i * 3 + 2] = 0.3;
            } else {
                // Orange to red flares
                colors[i * 3] = 1.0;
                colors[i * 3 + 1] = 0.4 + Math.random() * 0.4;
                colors[i * 3 + 2] = Math.random() * 0.2;
            }

            sizes[i] = Math.random() * 4 + 1;

            // Add velocity for dynamic movement
            velocities[i * 3] = (Math.random() - 0.5) * 0.02;
            velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.02;
            velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.02;
        }

        flareGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        flareGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        flareGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        flareGeometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

        const flareMaterial = new THREE.PointsMaterial({
            size: 3,
            vertexColors: true,
            transparent: true,
            opacity: 0.9,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        const solarFlares = new THREE.Points(flareGeometry, flareMaterial);
        solarFlares.name = 'SolarFlares';
        this.sun.add(solarFlares);
    }

    createCoronalMassEjections() {
        // Create dramatic coronal mass ejection streams
        const cmeCount = 8;
        for (let i = 0; i < cmeCount; i++) {
            const cmeGeometry = new THREE.BufferGeometry();
            const streamLength = 50;
            const positions = new Float32Array(streamLength * 3);
            const colors = new Float32Array(streamLength * 3);

            // Random direction for CME
            const direction = new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize();

            for (let j = 0; j < streamLength; j++) {
                const distance = 6 + j * 0.5;
                positions[j * 3] = direction.x * distance;
                positions[j * 3 + 1] = direction.y * distance;
                positions[j * 3 + 2] = direction.z * distance;

                // Fade from bright to dim along the stream
                const fade = 1.0 - (j / streamLength);
                colors[j * 3] = 1.0 * fade;
                colors[j * 3 + 1] = 0.6 * fade;
                colors[j * 3 + 2] = 0.2 * fade;
            }

            cmeGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            cmeGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const cmeMaterial = new THREE.LineBasicMaterial({
                vertexColors: true,
                transparent: true,
                opacity: 0.7,
                blending: THREE.AdditiveBlending,
                linewidth: 2
            });

            const cme = new THREE.Line(cmeGeometry, cmeMaterial);
            cme.name = `CME_${i}`;
            this.sun.add(cme);
        }
    }

    createSolarProminences() {
        // Create solar prominence arcs
        const prominenceCount = 6;
        for (let i = 0; i < prominenceCount; i++) {
            const prominenceGeometry = new THREE.BufferGeometry();
            const arcPoints = 30;
            const positions = new Float32Array(arcPoints * 3);
            const colors = new Float32Array(arcPoints * 3);

            // Create arc shape
            const startAngle = Math.random() * Math.PI * 2;
            const arcHeight = 3 + Math.random() * 4;

            for (let j = 0; j < arcPoints; j++) {
                const t = j / (arcPoints - 1);
                const angle = startAngle + t * Math.PI * 0.5;
                const height = Math.sin(t * Math.PI) * arcHeight;

                positions[j * 3] = Math.cos(angle) * (5.5 + height * 0.3);
                positions[j * 3 + 1] = height;
                positions[j * 3 + 2] = Math.sin(angle) * (5.5 + height * 0.3);

                // Prominence colors - red to orange
                const intensity = Math.sin(t * Math.PI);
                colors[j * 3] = 1.0;
                colors[j * 3 + 1] = 0.3 + intensity * 0.4;
                colors[j * 3 + 2] = 0.1;
            }

            prominenceGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            prominenceGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const prominenceMaterial = new THREE.LineBasicMaterial({
                vertexColors: true,
                transparent: true,
                opacity: 0.8,
                blending: THREE.AdditiveBlending,
                linewidth: 3
            });

            const prominence = new THREE.Line(prominenceGeometry, prominenceMaterial);
            prominence.name = `Prominence_${i}`;
            this.sun.add(prominence);
        }
    }

    createLensFlare() {
        // Create lens flare effect using sprites
        const lensFlareGroup = new THREE.Group();
        lensFlareGroup.name = 'LensFlare';

        // Main lens flare
        const mainFlareGeometry = new THREE.PlaneGeometry(20, 20);
        const mainFlareMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                opacity: { value: 0.0 },
                intensity: { value: 1.0 }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float opacity;
                uniform float intensity;
                varying vec2 vUv;

                void main() {
                    vec2 center = vec2(0.5, 0.5);
                    float dist = distance(vUv, center);

                    // Create lens flare pattern
                    float flare = 1.0 - smoothstep(0.0, 0.5, dist);
                    flare = pow(flare, 3.0);

                    // Add hexagonal pattern
                    float angle = atan(vUv.y - 0.5, vUv.x - 0.5);
                    float hexPattern = cos(angle * 6.0) * 0.1 + 0.9;

                    vec3 color = vec3(1.0, 0.9, 0.7) * flare * hexPattern * intensity;
                    gl_FragColor = vec4(color, opacity * flare);
                }
            `,
            transparent: true,
            blending: THREE.AdditiveBlending,
            depthWrite: false
        });

        const mainFlare = new THREE.Mesh(mainFlareGeometry, mainFlareMaterial);
        mainFlare.name = 'MainFlare';
        lensFlareGroup.add(mainFlare);

        // Secondary flares
        for (let i = 0; i < 5; i++) {
            const flareGeometry = new THREE.PlaneGeometry(5 + i * 2, 5 + i * 2);
            const flareMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    opacity: { value: 0.0 },
                    color: { value: new THREE.Color().setHSL(0.1 + i * 0.1, 0.8, 0.6) }
                },
                vertexShader: `
                    varying vec2 vUv;
                    void main() {
                        vUv = uv;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform float opacity;
                    uniform vec3 color;
                    varying vec2 vUv;

                    void main() {
                        vec2 center = vec2(0.5, 0.5);
                        float dist = distance(vUv, center);
                        float flare = 1.0 - smoothstep(0.0, 0.5, dist);
                        flare = pow(flare, 2.0);

                        gl_FragColor = vec4(color * flare, opacity * flare * 0.3);
                    }
                `,
                transparent: true,
                blending: THREE.AdditiveBlending,
                depthWrite: false
            });

            const flare = new THREE.Mesh(flareGeometry, flareMaterial);
            flare.position.set((i - 2) * 3, (i - 2) * 2, -1);
            flare.name = `SecondaryFlare_${i}`;
            lensFlareGroup.add(flare);
        }

        this.lensFlare = lensFlareGroup;
        this.scene.add(lensFlareGroup);
    }

    createSunGlow() {
        // Create massive sun glow effect
        const glowGeometry = new THREE.SphereGeometry(25, 32, 32);
        const glowMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                intensity: { value: 0.3 },
                pulseSpeed: { value: 2.0 }
            },
            vertexShader: `
                varying vec3 vNormal;
                varying vec3 vPosition;
                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    vPosition = position;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float intensity;
                uniform float pulseSpeed;
                varying vec3 vNormal;
                varying vec3 vPosition;

                void main() {
                    float rim = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
                    rim = pow(rim, 6.0);

                    // Add pulsing effect
                    float pulse = sin(time * pulseSpeed) * 0.3 + 0.7;

                    // Create radial gradient
                    float radial = 1.0 - length(vPosition) / 25.0;
                    radial = pow(radial, 3.0);

                    vec3 glowColor = vec3(1.0, 0.8, 0.4);
                    float alpha = rim * radial * intensity * pulse;

                    gl_FragColor = vec4(glowColor, alpha);
                }
            `,
            transparent: true,
            side: THREE.BackSide,
            blending: THREE.AdditiveBlending,
            depthWrite: false
        });

        const sunGlow = new THREE.Mesh(glowGeometry, glowMaterial);
        sunGlow.name = 'SunGlow';
        this.sun.add(sunGlow);
    }

    async createPlanet(data, index) {
        const planetGeometry = new THREE.SphereGeometry(data.radius, 64, 64);

        // Create realistic planet material based on planet type
        const planetMaterial = this.createPlanetMaterial(data);

        const planet = new THREE.Mesh(planetGeometry, planetMaterial);
        planet.position.x = data.distance;
        planet.castShadow = true;
        planet.receiveShadow = true;
        planet.name = data.name;
        planet.userData = {
            ...data,
            angle: Math.random() * Math.PI * 2,
            originalSpeed: data.speed,
            currentSpeed: data.speed,
            index: index,
            material: planetMaterial
        };

        this.planets.push(planet);
        this.scene.add(planet);

        // Add planet label
        this.createPlanetLabel(planet, data.name);

        // Add special effects for certain planets
        if (data.name === 'Saturn') {
            this.createSaturnRings(planet);
        } else if (data.name === 'Earth') {
            this.createEarthAtmosphere(planet);
            this.createEarthClouds(planet);
        } else if (data.name === 'Jupiter') {
            this.createJupiterEffect(planet);
            this.createJupiterStorm(planet);
        } else if (data.name === 'Mars') {
            this.createMarsDustStorm(planet);
        } else if (data.name === 'Venus') {
            this.createVenusAtmosphere(planet);
        }
    }

    createPlanetMaterial(data) {
        switch (data.name) {
            case 'Mercury':
                return new THREE.MeshPhongMaterial({
                    color: 0x8c7853,
                    shininess: 5,
                    specular: 0x222222,
                    bumpScale: 0.02
                });

            case 'Venus':
                return new THREE.MeshPhongMaterial({
                    color: 0xffc649,
                    shininess: 100,
                    specular: 0x444444,
                    emissive: 0x221100,
                    emissiveIntensity: 0.1
                });

            case 'Earth':
                return new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        oceanColor: { value: new THREE.Color(0x006994) },
                        landColor: { value: new THREE.Color(0x228B22) },
                        cloudColor: { value: new THREE.Color(0xffffff) },
                        iceColor: { value: new THREE.Color(0xf0f8ff) }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;
                        varying vec3 vNormal;
                        void main() {
                            vUv = uv;
                            vPosition = position;
                            vNormal = normalize(normalMatrix * normal);
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 oceanColor;
                        uniform vec3 landColor;
                        uniform vec3 cloudColor;
                        uniform vec3 iceColor;
                        varying vec2 vUv;
                        varying vec3 vPosition;
                        varying vec3 vNormal;

                        float noise(vec2 p) {
                            return sin(p.x * 12.0) * sin(p.y * 8.0);
                        }

                        void main() {
                            vec2 uv = vUv;
                            float n = noise(uv * 5.0);

                            // Create continents and oceans
                            vec3 color;
                            if (n > 0.2) {
                                color = landColor;
                            } else {
                                color = oceanColor;
                            }

                            // Add ice caps
                            if (abs(uv.y - 0.5) > 0.4) {
                                color = mix(color, iceColor, 0.7);
                            }

                            // Add some specular reflection for oceans
                            if (n <= 0.2) {
                                float fresnel = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 2.0);
                                color += fresnel * 0.3;
                            }

                            gl_FragColor = vec4(color, 1.0);
                        }
                    `
                });

            case 'Mars':
                return new THREE.MeshPhongMaterial({
                    color: 0xcd5c5c,
                    shininess: 10,
                    specular: 0x111111,
                    bumpScale: 0.03
                });

            case 'Jupiter':
                return new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        color1: { value: new THREE.Color(0xd8ca9d) },
                        color2: { value: new THREE.Color(0xb8860b) },
                        color3: { value: new THREE.Color(0x8b4513) }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;
                        void main() {
                            vUv = uv;
                            vPosition = position;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 color1;
                        uniform vec3 color2;
                        uniform vec3 color3;
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        float noise(vec2 p) {
                            return sin(p.x * 8.0 + time * 0.5) * sin(p.y * 6.0 + time * 0.3);
                        }

                        void main() {
                            float n = noise(vUv * 10.0);
                            vec3 color;
                            if (n > 0.3) {
                                color = mix(color2, color1, (n - 0.3) / 0.7);
                            } else if (n > -0.3) {
                                color = mix(color3, color2, (n + 0.3) / 0.6);
                            } else {
                                color = color3;
                            }
                            gl_FragColor = vec4(color, 1.0);
                        }
                    `
                });

            case 'Saturn':
                return new THREE.MeshPhongMaterial({
                    color: 0xfad5a5,
                    shininess: 30,
                    specular: 0x333333
                });

            case 'Uranus':
                return new THREE.MeshPhongMaterial({
                    color: 0x4fd0e7,
                    shininess: 80,
                    specular: 0x444444,
                    emissive: 0x001122,
                    emissiveIntensity: 0.05
                });

            case 'Neptune':
                return new THREE.MeshPhongMaterial({
                    color: 0x4b70dd,
                    shininess: 90,
                    specular: 0x555555,
                    emissive: 0x000033,
                    emissiveIntensity: 0.08
                });

            default:
                return new THREE.MeshPhongMaterial({
                    color: data.color,
                    shininess: 30,
                    specular: 0x222222
                });
        }
    }

    createPlanetLabel(planet, name) {
        // Create text sprite for planet label
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;

        context.fillStyle = 'rgba(0, 0, 0, 0.8)';
        context.fillRect(0, 0, canvas.width, canvas.height);

        context.fillStyle = '#ffffff';
        context.font = 'bold 24px Arial';
        context.textAlign = 'center';
        context.fillText(name, canvas.width / 2, canvas.height / 2 + 8);

        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({
            map: texture,
            transparent: true,
            opacity: 0.8
        });
        const sprite = new THREE.Sprite(spriteMaterial);
        sprite.scale.set(8, 2, 1);
        sprite.position.y = planet.userData.radius + 3;
        sprite.name = `${name}_label`;

        planet.add(sprite);
    }

    createSaturnRings(saturn) {
        // Create multiple ring segments for realism
        const ringSegments = [
            { inner: 2.5, outer: 3.0, opacity: 0.8, color: 0xfad5a5 },
            { inner: 3.1, outer: 3.4, opacity: 0.6, color: 0xe6c28a },
            { inner: 3.5, outer: 3.8, opacity: 0.4, color: 0xd4b896 },
            { inner: 3.9, outer: 4.2, opacity: 0.7, color: 0xc9a876 }
        ];

        ringSegments.forEach((segment, index) => {
            const ringGeometry = new THREE.RingGeometry(segment.inner, segment.outer, 64);
            const ringMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    innerRadius: { value: segment.inner },
                    outerRadius: { value: segment.outer },
                    color: { value: new THREE.Color(segment.color) },
                    opacity: { value: segment.opacity }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying float vRadius;
                    void main() {
                        vUv = uv;
                        vec3 pos = position;
                        vRadius = length(pos.xy);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform float innerRadius;
                    uniform float outerRadius;
                    uniform vec3 color;
                    uniform float opacity;
                    varying vec2 vUv;
                    varying float vRadius;

                    float noise(float x) {
                        return sin(x * 50.0 + time * 2.0) * 0.1;
                    }

                    void main() {
                        float normalizedRadius = (vRadius - innerRadius) / (outerRadius - innerRadius);
                        float alpha = opacity * (1.0 - abs(normalizedRadius - 0.5) * 2.0);

                        // Add some noise for particle effect
                        alpha *= (0.8 + noise(vRadius * 10.0));

                        gl_FragColor = vec4(color, alpha);
                    }
                `,
                transparent: true,
                side: THREE.DoubleSide,
                blending: THREE.NormalBlending
            });

            const rings = new THREE.Mesh(ringGeometry, ringMaterial);
            rings.rotation.x = Math.PI / 2;
            rings.name = `SaturnRing_${index}`;
            saturn.add(rings);
        });
    }

    createEarthAtmosphere(earth) {
        // Create realistic atmospheric scattering
        const atmosphereGeometry = new THREE.SphereGeometry(earth.userData.radius * 1.1, 64, 64);
        const atmosphereMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                viewVector: { value: new THREE.Vector3() },
                c: { value: 0.3 },
                p: { value: 6.0 }
            },
            vertexShader: `
                uniform vec3 viewVector;
                uniform float c;
                uniform float p;
                varying float intensity;
                varying vec3 vNormal;

                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    vec3 vNormel = normalize(normalMatrix * normal);
                    vec3 vViewPosition = normalize(viewVector - position);
                    intensity = pow(c - dot(vNormel, vViewPosition), p);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                varying float intensity;
                varying vec3 vNormal;

                void main() {
                    vec3 glow = vec3(0.3, 0.6, 1.0) * intensity;

                    // Add some atmospheric shimmer
                    float shimmer = sin(time * 2.0 + vNormal.x * 10.0) * 0.1 + 0.9;
                    glow *= shimmer;

                    gl_FragColor = vec4(glow, intensity * 0.8);
                }
            `,
            side: THREE.BackSide,
            blending: THREE.AdditiveBlending,
            transparent: true
        });

        const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        atmosphere.name = 'EarthAtmosphere';
        earth.add(atmosphere);
    }

    createEarthClouds(earth) {
        const cloudGeometry = new THREE.SphereGeometry(earth.userData.radius * 1.02, 32, 32);
        const cloudMaterial = new THREE.MeshPhongMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.4,
            side: THREE.FrontSide
        });
        const clouds = new THREE.Mesh(cloudGeometry, cloudMaterial);
        clouds.name = 'EarthClouds';
        earth.add(clouds);
    }

    createJupiterEffect(jupiter) {
        // Add a subtle glow effect to Jupiter
        const glowGeometry = new THREE.SphereGeometry(jupiter.userData.radius * 1.05, 32, 32);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0xd8ca9d,
            transparent: true,
            opacity: 0.1,
            side: THREE.BackSide
        });
        const glow = new THREE.Mesh(glowGeometry, glowMaterial);
        jupiter.add(glow);
    }

    createJupiterStorm(jupiter) {
        // Create the Great Red Spot
        const stormGeometry = new THREE.SphereGeometry(jupiter.userData.radius * 0.3, 16, 16);
        const stormMaterial = new THREE.MeshBasicMaterial({
            color: 0xff4444,
            transparent: true,
            opacity: 0.6
        });
        const storm = new THREE.Mesh(stormGeometry, stormMaterial);
        storm.position.set(jupiter.userData.radius * 0.8, 0, 0);
        storm.name = 'JupiterStorm';
        jupiter.add(storm);
    }

    createMarsDustStorm(mars) {
        // Create dust particles around Mars
        const dustCount = 100;
        const dustGeometry = new THREE.BufferGeometry();
        const positions = new Float32Array(dustCount * 3);

        for (let i = 0; i < dustCount; i++) {
            const radius = mars.userData.radius * (1.1 + Math.random() * 0.3);
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.random() * Math.PI;

            positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i * 3 + 2] = radius * Math.cos(phi);
        }

        dustGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const dustMaterial = new THREE.PointsMaterial({
            color: 0xcd853f,
            size: 0.1,
            transparent: true,
            opacity: 0.6
        });

        const dustStorm = new THREE.Points(dustGeometry, dustMaterial);
        dustStorm.name = 'MarsDust';
        mars.add(dustStorm);
    }

    createVenusAtmosphere(venus) {
        // Create thick, toxic atmosphere with sulfuric acid clouds
        const atmosphereGeometry = new THREE.SphereGeometry(venus.userData.radius * 1.15, 64, 64);
        const atmosphereMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                toxicColor1: { value: new THREE.Color(0xffaa00) },
                toxicColor2: { value: new THREE.Color(0xff6600) },
                density: { value: 0.8 }
            },
            vertexShader: `
                varying vec2 vUv;
                varying vec3 vNormal;
                varying vec3 vPosition;

                void main() {
                    vUv = uv;
                    vNormal = normalize(normalMatrix * normal);
                    vPosition = position;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform vec3 toxicColor1;
                uniform vec3 toxicColor2;
                uniform float density;
                varying vec2 vUv;
                varying vec3 vNormal;
                varying vec3 vPosition;

                float noise(vec3 p) {
                    return sin(p.x * 8.0 + time) * sin(p.y * 6.0 + time * 0.7) * sin(p.z * 4.0 + time * 1.3);
                }

                void main() {
                    float n = noise(vPosition * 3.0);
                    vec3 color = mix(toxicColor1, toxicColor2, (n + 1.0) * 0.5);

                    // Create rim lighting for thick atmosphere
                    float rim = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
                    rim = pow(rim, 1.5);

                    float alpha = density * rim * (0.8 + n * 0.2);
                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: true,
            side: THREE.BackSide,
            blending: THREE.AdditiveBlending
        });

        const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        atmosphere.name = 'VenusAtmosphere';
        venus.add(atmosphere);
    }

    createOrbitLines() {
        this.orbitLines = [];
        
        this.planetData.forEach(data => {
            const points = [];
            const segments = 128;
            
            for (let i = 0; i <= segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                points.push(new THREE.Vector3(
                    Math.cos(angle) * data.distance,
                    0,
                    Math.sin(angle) * data.distance
                ));
            }
            
            const orbitGeometry = new THREE.BufferGeometry().setFromPoints(points);
            const orbitMaterial = new THREE.LineBasicMaterial({
                color: 0x444444,
                transparent: true,
                opacity: 0.3
            });
            
            const orbitLine = new THREE.Line(orbitGeometry, orbitMaterial);
            orbitLine.name = `${data.name}_orbit`;
            this.orbitLines.push(orbitLine);
            this.scene.add(orbitLine);
        });
    }

    setupControls() {
        // Setup orbit controls with error checking
        try {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.minDistance = 10;
            this.controls.maxDistance = 500;
            this.controls.enablePan = true;
            this.controls.enableZoom = true;
            this.controls.enableRotate = true;

            console.log('OrbitControls initialized successfully');
        } catch (error) {
            console.error('Failed to initialize OrbitControls:', error);
            // Create a minimal fallback
            this.controls = {
                enableDamping: true,
                dampingFactor: 0.05,
                minDistance: 10,
                maxDistance: 500,
                enablePan: true,
                enableZoom: true,
                enableRotate: true,
                rotateSpeed: 1.0,
                zoomSpeed: 1.0,
                panSpeed: 1.0,
                update: function() {},
                dispose: function() {}
            };
        }

        // Set initial camera speed
        this.controls.rotateSpeed = this.cameraSpeed;
        this.controls.zoomSpeed = this.cameraSpeed;
        this.controls.panSpeed = this.cameraSpeed;

        // Mobile optimizations
        if (this.isMobile()) {
            this.controls.rotateSpeed = 0.5 * this.cameraSpeed;
            this.controls.zoomSpeed = 0.8 * this.cameraSpeed;
            this.controls.panSpeed = 0.8 * this.cameraSpeed;
            this.controls.touches = {
                ONE: THREE.TOUCH.ROTATE,
                TWO: THREE.TOUCH.DOLLY_PAN
            };
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth < 768;
    }

    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Mouse events for planet interaction
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        this.renderer.domElement.addEventListener('click', (event) => this.onMouseClick(event));
        
        // UI controls
        this.setupUIControls();
    }

    setupUIControls() {
        // Global controls
        const playPauseBtn = document.getElementById('play-pause-btn');
        const resetBtn = document.getElementById('reset-btn');
        const globalSpeedSlider = document.getElementById('global-speed');
        const cameraSpeedSlider = document.getElementById('camera-speed');
        
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        
        // View options
        const showOrbitsCheckbox = document.getElementById('show-orbits');
        const showLabelsCheckbox = document.getElementById('show-labels');
        const showStarsCheckbox = document.getElementById('show-stars');
        const showTrailsCheckbox = document.getElementById('show-trails');
        const realisticSizesCheckbox = document.getElementById('realistic-sizes');
        
        // Panel toggle
        const panelToggle = document.getElementById('panel-toggle');
        const controlPanel = document.getElementById('control-panel');
        
        // Info modal
        const infoBtn = document.getElementById('info-btn');
        const infoModal = document.getElementById('info-modal');
        const closeModal = document.getElementById('close-modal');
        
        // Fullscreen
        const fullscreenBtn = document.getElementById('fullscreen-btn');

        // Event listeners
        playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        resetBtn.addEventListener('click', () => this.resetSimulation());
        globalSpeedSlider.addEventListener('input', (e) => this.setGlobalSpeed(parseFloat(e.target.value)));
        cameraSpeedSlider.addEventListener('input', (e) => {
            const speed = parseFloat(e.target.value);
            console.log('Camera speed slider changed to:', speed);
            this.setCameraSpeed(speed);
        });
        
        themeToggle.addEventListener('click', () => this.toggleTheme());
        
        showOrbitsCheckbox.addEventListener('change', (e) => this.toggleOrbits(e.target.checked));
        showLabelsCheckbox.addEventListener('change', (e) => this.toggleLabels(e.target.checked));
        showStarsCheckbox.addEventListener('change', (e) => this.toggleStars(e.target.checked));
        showTrailsCheckbox.addEventListener('change', (e) => this.toggleTrails(e.target.checked));
        realisticSizesCheckbox.addEventListener('change', (e) => this.toggleRealisticSizes(e.target.checked));
        
        panelToggle.addEventListener('click', () => {
            controlPanel.classList.toggle('collapsed');
            const icon = panelToggle.querySelector('i');
            icon.className = controlPanel.classList.contains('collapsed') ? 'fas fa-chevron-right' : 'fas fa-chevron-left';
        });
        
        infoBtn.addEventListener('click', () => infoModal.classList.remove('hidden'));
        closeModal.addEventListener('click', () => infoModal.classList.add('hidden'));
        infoModal.addEventListener('click', (e) => {
            if (e.target === infoModal) infoModal.classList.add('hidden');
        });
        
        fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());

        // Generate planet controls
        this.generatePlanetControls();

        // Test camera speed functionality
        console.log('Testing camera speed functionality...');
        setTimeout(() => {
            this.testCameraSpeed();
        }, 1000);
    }

    generatePlanetControls() {
        const planetControlsContainer = document.getElementById('planet-controls');
        
        this.planetData.forEach((data, index) => {
            const controlDiv = document.createElement('div');
            controlDiv.className = 'control-item';
            controlDiv.innerHTML = `
                <label for="planet-${index}-speed">${data.name} Speed</label>
                <div class="slider-container">
                    <input type="range" id="planet-${index}-speed" min="0" max="5" step="0.1" value="1">
                    <span class="slider-value">1.0x</span>
                </div>
            `;
            
            const slider = controlDiv.querySelector('input');
            const valueSpan = controlDiv.querySelector('.slider-value');
            
            slider.addEventListener('input', (e) => {
                const speed = parseFloat(e.target.value);
                valueSpan.textContent = `${speed.toFixed(1)}x`;
                this.setPlanetSpeed(index, speed);
            });
            
            planetControlsContainer.appendChild(controlDiv);
        });
    }

    // Animation and update methods
    animate() {
        requestAnimationFrame(() => this.animate());

        // Performance monitoring
        this.frameCount++;
        const now = performance.now();
        if (now - this.lastFPSUpdate > 1000) {
            this.currentFPS = this.frameCount;
            this.frameCount = 0;
            this.lastFPSUpdate = now;

            // Adaptive quality based on FPS
            this.adaptQuality();
        }

        if (this.isPlaying) {
            this.updatePlanets();
            this.updateSun();
            if (this.showTrails && this.currentFPS > 30) {
                this.updatePlanetTrails();
            }
        }

        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }

    adaptQuality() {
        // Reduce quality if FPS is too low
        if (this.currentFPS < 30 && this.renderer.getPixelRatio() > 1) {
            this.renderer.setPixelRatio(1);
        } else if (this.currentFPS > 50 && this.renderer.getPixelRatio() < window.devicePixelRatio) {
            this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }

        // Disable trails on low-end devices
        if (this.currentFPS < 25 && this.showTrails) {
            this.toggleTrails(false);
            document.getElementById('show-trails').checked = false;
        }
    }

    updatePlanets() {
        const deltaTime = this.clock.getDelta();
        const elapsedTime = this.clock.getElapsedTime();

        this.planets.forEach(planet => {
            const data = planet.userData;

            // Update orbital position
            data.angle += data.currentSpeed * this.globalSpeed * deltaTime * 0.1;
            planet.position.x = Math.cos(data.angle) * data.distance;
            planet.position.z = Math.sin(data.angle) * data.distance;

            // Update rotation
            planet.rotation.y += data.rotationSpeed * this.globalSpeed * deltaTime * 10;

            // Update shader materials (like Jupiter)
            if (data.material && data.material.uniforms && data.material.uniforms.time) {
                data.material.uniforms.time.value = elapsedTime * this.globalSpeed;
            }

            // Update special effects
            planet.children.forEach(child => {
                if (child.name === 'EarthClouds') {
                    child.rotation.y += 0.008 * this.globalSpeed * deltaTime * 10;
                } else if (child.name === 'JupiterStorm') {
                    child.rotation.y += 0.015 * this.globalSpeed * deltaTime * 10;
                } else if (child.name === 'MarsDust') {
                    child.rotation.y += 0.012 * this.globalSpeed * deltaTime * 10;
                    child.rotation.x += 0.005 * this.globalSpeed * deltaTime * 10;
                } else if (child.name === 'VenusAtmosphere') {
                    child.rotation.y -= 0.003 * this.globalSpeed * deltaTime * 10; // Retrograde
                    // Update Venus atmosphere shader
                    if (child.material && child.material.uniforms && child.material.uniforms.time) {
                        child.material.uniforms.time.value = elapsedTime * this.globalSpeed;
                    }
                } else if (child.name === 'EarthAtmosphere') {
                    // Update Earth atmosphere shader
                    if (child.material && child.material.uniforms) {
                        if (child.material.uniforms.time) {
                            child.material.uniforms.time.value = elapsedTime * this.globalSpeed;
                        }
                        if (child.material.uniforms.viewVector) {
                            child.material.uniforms.viewVector.value.copy(this.camera.position);
                        }
                    }
                } else if (child.name && child.name.startsWith('SaturnRing_')) {
                    // Update Saturn ring shaders
                    if (child.material && child.material.uniforms && child.material.uniforms.time) {
                        child.material.uniforms.time.value = elapsedTime * this.globalSpeed;
                    }
                    // Rings rotate at different speeds
                    const ringIndex = parseInt(child.name.split('_')[1]);
                    child.rotation.z += (0.001 + ringIndex * 0.0002) * this.globalSpeed * deltaTime * 10;
                }
            });
        });
    }

    updateSun() {
        const deltaTime = this.clock.getDelta();
        const elapsedTime = this.clock.getElapsedTime();

        // Rotate the sun
        this.sun.rotation.y += 0.005 * this.globalSpeed * deltaTime * 10;

        // Update shader uniforms for animated surface
        if (this.sun.userData.material && this.sun.userData.material.uniforms) {
            this.sun.userData.material.uniforms.time.value = elapsedTime * this.globalSpeed;
        }

        // Dynamic lighting effects
        this.updateDynamicLighting(elapsedTime);

        // Update corona and flare effects
        this.sun.children.forEach(child => {
            if (child.material && child.material.uniforms && child.material.uniforms.time) {
                child.material.uniforms.time.value = elapsedTime * this.globalSpeed * 0.5;
            }

            // Animate solar flares with dynamic movement
            if (child.name === 'SolarFlares') {
                child.rotation.y += 0.003 * this.globalSpeed * deltaTime * 10;
                child.rotation.x += 0.002 * this.globalSpeed * deltaTime * 10;

                // Update flare particle positions
                this.updateSolarFlareParticles(child, deltaTime);
            }

            // Animate coronal mass ejections
            if (child.name && child.name.startsWith('CME_')) {
                child.rotation.y += 0.001 * this.globalSpeed * deltaTime * 10;
                child.rotation.z += 0.0005 * this.globalSpeed * deltaTime * 10;
            }

            // Animate solar prominences
            if (child.name && child.name.startsWith('Prominence_')) {
                child.rotation.y += 0.0008 * this.globalSpeed * deltaTime * 10;
            }

            // Update corona layers
            if (child.name === 'InnerCorona' || child.name === 'OuterCorona' || child.name === 'ExtremeCorona') {
                child.rotation.y += 0.0003 * this.globalSpeed * deltaTime * 10;
                child.rotation.x += 0.0001 * this.globalSpeed * deltaTime * 10;
            }

            // Update sun glow
            if (child.name === 'SunGlow' && child.material && child.material.uniforms) {
                child.material.uniforms.time.value = elapsedTime * this.globalSpeed;
                // Increase glow intensity during solar flares
                const flareBoost = this.flareLight ? this.flareLight.intensity * 0.1 : 0;
                child.material.uniforms.intensity.value = 0.3 + flareBoost;
            }
        });
    }

    updateDynamicLighting(elapsedTime) {
        // Create pulsing sun intensity
        const basePulse = Math.sin(elapsedTime * 2.0) * 0.3 + 1.0;
        const fastPulse = Math.sin(elapsedTime * 8.0) * 0.1 + 1.0;
        const slowPulse = Math.sin(elapsedTime * 0.5) * 0.2 + 1.0;

        // Update main sun light
        if (this.sunLight) {
            this.sunLight.intensity = this.baseSunIntensity * basePulse * this.globalSpeed;
        }

        // Update secondary sun light
        if (this.sunLight2) {
            this.sunLight2.intensity = 2 * fastPulse * this.globalSpeed;
        }

        // Create dramatic solar flare events
        const flareEvent = Math.sin(elapsedTime * 3.0 + Math.sin(elapsedTime * 0.7) * 5.0);
        if (flareEvent > 0.8) {
            // Solar flare is happening!
            const flareIntensity = (flareEvent - 0.8) * 10;
            if (this.flareLight) {
                this.flareLight.intensity = flareIntensity * 3 * this.globalSpeed;
            }
        } else {
            if (this.flareLight) {
                this.flareLight.intensity = 0;
            }
        }

        // Update corona light
        if (this.coronaLight) {
            this.coronaLight.intensity = this.baseCoronaIntensity * slowPulse * this.globalSpeed;
        }

        // Update lens flare based on camera angle to sun
        this.updateLensFlare();
    }

    updateLensFlare() {
        if (!this.lensFlare || !this.camera) return;

        // Calculate angle between camera direction and sun
        const cameraDirection = new THREE.Vector3();
        this.camera.getWorldDirection(cameraDirection);

        const sunDirection = new THREE.Vector3();
        sunDirection.subVectors(this.sun.position, this.camera.position).normalize();

        const dot = cameraDirection.dot(sunDirection);
        const angle = Math.acos(Math.max(-1, Math.min(1, dot)));

        // Calculate lens flare intensity based on angle
        const maxAngle = Math.PI / 6; // 30 degrees
        let flareIntensity = 0;

        if (angle < maxAngle) {
            flareIntensity = 1.0 - (angle / maxAngle);
            flareIntensity = Math.pow(flareIntensity, 2); // Smooth falloff
        }

        // Position lens flare in screen space
        const sunScreenPosition = new THREE.Vector3();
        sunScreenPosition.copy(this.sun.position);
        sunScreenPosition.project(this.camera);

        // Update lens flare position and opacity
        this.lensFlare.children.forEach((flare, index) => {
            if (flare.material && flare.material.uniforms) {
                if (flare.material.uniforms.opacity) {
                    flare.material.uniforms.opacity.value = flareIntensity * 0.8;
                }
                if (flare.material.uniforms.time) {
                    flare.material.uniforms.time.value = this.clock.getElapsedTime();
                }
            }

            // Position secondary flares along screen line
            if (index > 0) {
                const offset = (index - 2) * 0.2;
                flare.position.x = sunScreenPosition.x * offset * 10;
                flare.position.y = sunScreenPosition.y * offset * 10;
            }
        });

        // Position the main flare group
        this.lensFlare.position.set(
            sunScreenPosition.x * 5,
            sunScreenPosition.y * 5,
            -10
        );

        // Make lens flare face camera
        this.lensFlare.lookAt(this.camera.position);
    }

    updateSolarFlareParticles(flareSystem, deltaTime) {
        if (flareSystem.geometry && flareSystem.geometry.attributes.position) {
            const positions = flareSystem.geometry.attributes.position.array;
            const velocities = flareSystem.geometry.attributes.velocity.array;

            for (let i = 0; i < positions.length; i += 3) {
                // Apply velocity to position
                positions[i] += velocities[i] * deltaTime * 100 * this.globalSpeed;
                positions[i + 1] += velocities[i + 1] * deltaTime * 100 * this.globalSpeed;
                positions[i + 2] += velocities[i + 2] * deltaTime * 100 * this.globalSpeed;

                // Reset particles that go too far
                const distance = Math.sqrt(positions[i] * positions[i] + positions[i + 1] * positions[i + 1] + positions[i + 2] * positions[i + 2]);
                if (distance > 8) {
                    // Reset to sun surface
                    const radius = 5.2 + Math.random() * 1.5;
                    const theta = Math.random() * Math.PI * 2;
                    const phi = Math.random() * Math.PI;

                    positions[i] = radius * Math.sin(phi) * Math.cos(theta);
                    positions[i + 1] = radius * Math.sin(phi) * Math.sin(theta);
                    positions[i + 2] = radius * Math.cos(phi);
                }
            }

            flareSystem.geometry.attributes.position.needsUpdate = true;
        }
    }

    updatePlanetTrails() {
        this.planetTrails.forEach((trail, index) => {
            if (this.planets[index]) {
                const planet = this.planets[index];
                const trailData = trail.userData;

                // Add current planet position to trail
                trailData.positions.push(planet.position.clone());

                // Limit trail length
                if (trailData.positions.length > trailData.maxLength) {
                    trailData.positions.shift();
                }

                // Update trail geometry
                const positions = trail.geometry.attributes.position.array;
                for (let i = 0; i < trailData.positions.length; i++) {
                    const pos = trailData.positions[i];
                    positions[i * 3] = pos.x;
                    positions[i * 3 + 1] = pos.y;
                    positions[i * 3 + 2] = pos.z;
                }

                // Clear remaining positions
                for (let i = trailData.positions.length; i < trailData.maxLength; i++) {
                    positions[i * 3] = 0;
                    positions[i * 3 + 1] = 0;
                    positions[i * 3 + 2] = 0;
                }

                trail.geometry.attributes.position.needsUpdate = true;
                trail.geometry.setDrawRange(0, trailData.positions.length);
            }
        });
    }

    // Control methods
    togglePlayPause() {
        this.isPlaying = !this.isPlaying;
        const btn = document.getElementById('play-pause-btn');
        const icon = btn.querySelector('i');
        const text = btn.querySelector('span');
        
        if (this.isPlaying) {
            icon.className = 'fas fa-pause';
            text.textContent = 'Pause';
        } else {
            icon.className = 'fas fa-play';
            text.textContent = 'Play';
        }
    }

    resetSimulation() {
        this.planets.forEach(planet => {
            const data = planet.userData;
            data.angle = Math.random() * Math.PI * 2;
            data.currentSpeed = data.originalSpeed;
        });
        
        // Reset UI controls
        document.getElementById('global-speed').value = 1;
        document.querySelector('#global-speed + .slider-value').textContent = '1.0x';
        this.globalSpeed = 1.0;

        // Reset camera speed
        document.getElementById('camera-speed').value = 1;
        document.querySelector('#camera-speed + .slider-value').textContent = '1.0x';
        this.setCameraSpeed(1.0);
        
        // Reset planet speed controls
        this.planetData.forEach((_, index) => {
            const slider = document.getElementById(`planet-${index}-speed`);
            const valueSpan = slider.parentElement.querySelector('.slider-value');
            slider.value = 1;
            valueSpan.textContent = '1.0x';
        });
    }

    setGlobalSpeed(speed) {
        this.globalSpeed = speed;
        document.querySelector('#global-speed + .slider-value').textContent = `${speed.toFixed(1)}x`;
    }

    setCameraSpeed(speed) {
        this.cameraSpeed = speed;

        // Update OrbitControls speeds
        if (this.controls) {
            // These are the correct property names for OrbitControls
            this.controls.rotateSpeed = speed;
            this.controls.zoomSpeed = speed;
            this.controls.panSpeed = speed;

            // Also update autoRotateSpeed if auto-rotate is enabled
            if (this.controls.autoRotateSpeed !== undefined) {
                this.controls.autoRotateSpeed = speed * 2.0;
            }

            console.log(`Camera speed set to: ${speed}x`);
            console.log('Controls speeds:', {
                rotate: this.controls.rotateSpeed,
                zoom: this.controls.zoomSpeed,
                pan: this.controls.panSpeed
            });
        }

        // Update UI display
        const valueElement = document.querySelector('#camera-speed + .slider-value');
        if (valueElement) {
            valueElement.textContent = `${speed.toFixed(1)}x`;
        }

        // Update status indicator
        const statusElement = document.getElementById('camera-speed-status');
        if (statusElement) {
            statusElement.textContent = `Camera speed updated to ${speed.toFixed(1)}x - Try rotating/zooming to feel the difference`;
            statusElement.style.color = '#4CAF50';

            // Reset status after 3 seconds
            setTimeout(() => {
                statusElement.textContent = 'Move the slider to test camera speed';
                statusElement.style.color = '#888';
            }, 3000);
        }
    }

    testCameraSpeed() {
        console.log('=== Camera Speed Test ===');
        console.log('Controls object:', this.controls);

        if (this.controls) {
            console.log('Current camera speeds:', {
                rotateSpeed: this.controls.rotateSpeed,
                zoomSpeed: this.controls.zoomSpeed,
                panSpeed: this.controls.panSpeed,
                cameraSpeed: this.cameraSpeed
            });

            // Test setting different speeds
            console.log('Testing speed changes...');
            this.setCameraSpeed(0.5);
            setTimeout(() => {
                this.setCameraSpeed(2.0);
                setTimeout(() => {
                    this.setCameraSpeed(1.0);
                    console.log('Camera speed test completed');
                }, 1000);
            }, 1000);
        } else {
            console.error('Controls not available for testing');
        }
    }

    setPlanetSpeed(index, speed) {
        if (this.planets[index]) {
            this.planets[index].userData.currentSpeed = this.planets[index].userData.originalSpeed * speed;
        }
    }

    toggleOrbits(show) {
        this.showOrbits = show;
        this.orbitLines.forEach(line => {
            line.visible = show;
        });
    }

    toggleLabels(show) {
        this.showLabels = show;
        this.planets.forEach(planet => {
            const label = planet.children.find(child => child.name.includes('_label'));
            if (label) {
                label.visible = show;
            }
        });
    }

    toggleStars(show) {
        this.showStars = show;
        if (this.starField) {
            this.starField.visible = show;
        }
    }

    toggleTrails(show) {
        this.showTrails = show;
        this.planetTrails.forEach(trail => {
            trail.visible = show;
        });
    }

    toggleRealisticSizes(realistic) {
        this.realisticSizes = realistic;

        this.planets.forEach(planet => {
            const data = planet.userData;
            let newRadius;

            if (realistic) {
                // Use more realistic size ratios (still scaled for visibility)
                const realSizeRatios = {
                    'Mercury': 0.1,
                    'Venus': 0.2,
                    'Earth': 0.25,
                    'Mars': 0.15,
                    'Jupiter': 2.8,
                    'Saturn': 2.3,
                    'Uranus': 1.0,
                    'Neptune': 0.95
                };
                newRadius = realSizeRatios[data.name] || data.radius;
            } else {
                // Use the original scaled sizes for better visibility
                newRadius = data.radius;
            }

            // Update planet geometry
            planet.geometry.dispose();
            planet.geometry = new THREE.SphereGeometry(newRadius, 32, 32);

            // Update label position
            const label = planet.children.find(child => child.name.includes('_label'));
            if (label) {
                label.position.y = newRadius + 3;
            }
        });

        // Update sun size if realistic
        if (this.sun) {
            const newSunRadius = realistic ? 8 : 5;
            this.sun.geometry.dispose();
            this.sun.geometry = new THREE.SphereGeometry(newSunRadius, 32, 32);

            // Update sun glow
            const sunGlow = this.sun.children[0];
            if (sunGlow) {
                sunGlow.geometry.dispose();
                sunGlow.geometry = new THREE.SphereGeometry(newSunRadius * 1.4, 32, 32);
            }
        }
    }

    toggleTheme() {
        this.isDarkTheme = !this.isDarkTheme;
        const body = document.body;
        const themeIcon = document.querySelector('#theme-toggle i');
        
        if (this.isDarkTheme) {
            body.removeAttribute('data-theme');
            themeIcon.className = 'fas fa-moon';
        } else {
            body.setAttribute('data-theme', 'light');
            themeIcon.className = 'fas fa-sun';
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    // Mouse interaction methods
    onMouseMove(event) {
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.planets);
        
        if (intersects.length > 0) {
            const planet = intersects[0].object;
            this.showPlanetTooltip(planet, event);
            this.hoveredPlanet = planet;
        } else {
            this.hidePlanetTooltip();
            this.hoveredPlanet = null;
        }
    }

    onMouseClick() {
        if (this.hoveredPlanet) {
            this.focusOnPlanet(this.hoveredPlanet);
        }
    }

    showPlanetTooltip(planet, event) {
        const tooltip = document.getElementById('planet-tooltip');
        const data = planet.userData;
        
        document.getElementById('tooltip-name').textContent = data.name;
        document.getElementById('tooltip-info').textContent = data.info;
        document.getElementById('tooltip-distance').textContent = data.realDistance;
        document.getElementById('tooltip-period').textContent = data.realPeriod;
        
        tooltip.style.left = `${event.clientX + 10}px`;
        tooltip.style.top = `${event.clientY - 10}px`;
        tooltip.classList.remove('hidden');
    }

    hidePlanetTooltip() {
        document.getElementById('planet-tooltip').classList.add('hidden');
    }

    focusOnPlanet(planet) {
        const targetPosition = planet.position.clone();
        targetPosition.y += 10;
        targetPosition.multiplyScalar(1.5);
        
        // Smooth camera transition (simplified)
        this.controls.target.copy(planet.position);
        this.camera.position.copy(targetPosition);
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / (window.innerHeight - 80);
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight - 80);
    }
}

// Initialize the simulation when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SolarSystemSimulation();
});
